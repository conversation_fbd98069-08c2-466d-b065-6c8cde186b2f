<template>
  <div class="code-playground">
    <div class="playground-header">
      <h3>{{ title }}</h3>
      <div class="playground-controls">
        <select v-model="selectedExample" @change="loadExample" class="example-selector">
          <option value="">选择示例</option>
          <option v-for="(example, index) in examples" :key="index" :value="index">
            {{ example.title }}
          </option>
        </select>
        <button @click="runCode" class="run-button" :disabled="!code.trim()">▶️ 运行代码</button>
        <button @click="clearCode" class="clear-button">🗑️ 清空</button>
      </div>
    </div>

    <div class="playground-content">
      <div class="code-section">
        <div class="section-header">
          <h4>📝 代码编辑器</h4>
          <div class="editor-info">
            <span class="language-tag">Java</span>
            <span class="line-count">{{ lineCount }} 行</span>
          </div>
        </div>
        <div class="code-editor">
          <textarea
            v-model="code"
            @input="updateLineCount"
            class="code-textarea"
            placeholder="在这里输入你的Java代码..."
            spellcheck="false"
          ></textarea>
          <div class="line-numbers">
            <div v-for="n in lineCount" :key="n" class="line-number">{{ n }}</div>
          </div>
        </div>
      </div>

      <div class="output-section">
        <div class="section-header">
          <h4>📤 运行结果</h4>
          <div class="output-controls">
            <button @click="clearOutput" class="clear-output-button">清空输出</button>
          </div>
        </div>
        <div class="output-console">
          <div v-if="!output && !error" class="output-placeholder">点击"运行代码"查看结果...</div>
          <div v-if="output" class="output-content">
            <div class="output-label">✅ 输出:</div>
            <pre class="output-text">{{ output }}</pre>
          </div>
          <div v-if="error" class="error-content">
            <div class="error-label">❌ 错误:</div>
            <pre class="error-text">{{ error }}</pre>
          </div>
        </div>
      </div>
    </div>

    <div v-if="currentExample" class="example-explanation">
      <h4>💡 示例说明</h4>
      <p>{{ currentExample.explanation }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface CodeExample {
  title: string
  code: string
  explanation: string
}

const props = defineProps<{
  examples: CodeExample[]
  title: string
}>()

const emit = defineEmits<{
  codeRun: [result: { code: string; output?: string; error?: string }]
}>()

const code = ref('')
const output = ref('')
const error = ref('')
const selectedExample = ref('')
const lineCount = ref(1)

const currentExample = computed(() => {
  const index = parseInt(selectedExample.value)
  return isNaN(index) ? null : props.examples[index]
})

const loadExample = () => {
  if (currentExample.value) {
    code.value = currentExample.value.code
    updateLineCount()
    clearOutput()
  }
}

const runCode = () => {
  // 模拟代码执行
  clearOutput()

  try {
    // 这里可以集成真实的Java代码执行引擎
    // 目前只是模拟输出
    if (code.value.includes('System.out.println')) {
      const matches = code.value.match(/System\.out\.println\s*\(\s*"([^"]*)"\s*\)/g)
      if (matches) {
        output.value = matches.map((match) => match.match(/"([^"]*)"/)?.[1] || '').join('\n')
      }
    } else if (code.value.includes('var ')) {
      output.value = '代码编译成功！\n类型推断正常工作。'
    } else {
      output.value = '代码执行完成。'
    }

    emit('codeRun', { code: code.value, output: output.value })
  } catch (e) {
    error.value = '代码执行出错: ' + (e as Error).message
    emit('codeRun', { code: code.value, error: error.value })
  }
}

const clearCode = () => {
  code.value = ''
  selectedExample.value = ''
  updateLineCount()
  clearOutput()
}

const clearOutput = () => {
  output.value = ''
  error.value = ''
}

const updateLineCount = () => {
  lineCount.value = Math.max(1, code.value.split('\n').length)
}

watch(code, updateLineCount)
</script>

<style scoped>
.code-playground {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.playground-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-content {
  flex: 1;
}

.playground-title {
  color: #333;
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.playground-description {
  color: #666;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #bbb;
  transform: translateY(-1px);
}

.format-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.reset-btn:hover {
  background: #fce4ec;
  border-color: #e91e63;
  color: #c2185b;
}

.playground-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.section-title {
  color: #333;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
}

.section-hint {
  color: #999;
  font-size: 0.8rem;
  font-style: italic;
}

.code-editor {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.code-textarea {
  width: 100%;
  height: 300px;
  padding: 1rem;
  border: none;
  background: transparent;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #333;
  resize: none;
  outline: none;
}

.editor-footer {
  background: #e9ecef;
  padding: 0.75rem 1rem;
  border-top: 1px solid #dee2e6;
}

.run-btn {
  padding: 0.6rem 1.5rem;
  border: none;
  border-radius: 6px;
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.run-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #45a049, #4caf50);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.run-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.run-btn.running {
  background: linear-gradient(45deg, #ff9800, #f57c00);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.output-container {
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333;
}

.output-content {
  width: 100%;
  height: 300px;
  padding: 1rem;
  margin: 0;
  background: transparent;
  color: #d4d4d4;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.output-content.empty {
  color: #888;
  font-style: italic;
}

.output-content.has-error {
  color: #ff6b6b;
}

.playground-tips {
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 10px;
  padding: 1.5rem;
  border: 1px solid #e3f2fd;
}

.tips-title {
  color: #1976d2;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.tips-list {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
  padding-left: 1.2rem;
}

.tips-list li {
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .code-playground {
    padding: 1rem;
  }

  .playground-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    align-self: stretch;
  }

  .action-btn {
    flex: 1;
  }

  .playground-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .code-textarea,
  .output-content {
    height: 250px;
  }

  .playground-title {
    font-size: 1.3rem;
  }

  .tips-list {
    font-size: 0.85rem;
  }
}

/* 代码高亮样式（简单版） */
.code-textarea {
  tab-size: 2;
}

.code-textarea:focus {
  background: #ffffff;
  border: 2px solid #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 滚动条样式 */
.output-content::-webkit-scrollbar {
  width: 8px;
}

.output-content::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.output-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.output-content::-webkit-scrollbar-thumb:hover {
  background: #777;
}
</style>
