<template>
  <div class="code-playground">
    <div class="playground-header">
      <div class="header-content">
        <h3 class="playground-title">{{ title }}</h3>
        <p class="playground-description">{{ description }}</p>
      </div>
      <div class="header-actions">
        <button @click="formatCode" class="action-btn format-btn" title="格式化代码">
          📝 格式化
        </button>
        <button @click="resetCode" class="action-btn reset-btn" title="重置为原始代码">
          🔄 重置
        </button>
      </div>
    </div>

    <div class="playground-content">
      <!-- 代码编辑区 -->
      <div class="code-section">
        <div class="section-header">
          <h4 class="section-title">📝 代码编辑器</h4>
          <span class="section-hint">可以修改下面的代码并运行看效果</span>
        </div>

        <div class="code-editor">
          <textarea
            v-model="code"
            class="code-textarea"
            spellcheck="false"
            placeholder="在这里编写或修改代码..."
          ></textarea>

          <div class="editor-footer">
            <button
              @click="runCode"
              :disabled="isRunning"
              class="run-btn"
              :class="{ running: isRunning }"
            >
              {{ isRunning ? '🔄 运行中...' : '▶️ 运行代码' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 输出结果区 -->
      <div class="output-section">
        <div class="section-header">
          <h4 class="section-title">📤 执行结果</h4>
          <span class="section-hint">代码运行后的输出结果</span>
        </div>

        <div class="output-container">
          <pre class="output-content" :class="{ 'has-error': hasError, empty: !output }">{{
            output || '点击"运行代码"按钮来查看结果...'
          }}</pre>
        </div>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="playground-tips">
      <div class="tips-content">
        <h4 class="tips-title">💡 使用提示</h4>
        <ul class="tips-list">
          <li>你可以修改代码中的任何部分，比如改变字符串内容、变量名等</li>
          <li>代码会被自动转换为JavaScript执行，所以结果可能与真实的Java略有不同</li>
          <li>如果代码运行出错，检查语法是否正确，特别是引号、分号等符号</li>
          <li>点击"重置"按钮可以恢复到原始代码状态</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'

interface Props {
  title: string
  description: string
  initialCode: string
}

const props = defineProps<Props>()

const code = ref(props.initialCode)
const output = ref('')
const isRunning = ref(false)
const hasError = ref(false)

// 创建一个安全的JavaScript执行环境
const createSafeConsole = () => {
  const logs: string[] = []

  return {
    log: (...args: any[]) => {
      logs.push(
        args
          .map((arg) => (typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)))
          .join(' '),
      )
    },
    getLogs: () => logs,
    clear: () => (logs.length = 0),
  }
}

const runCode = async () => {
  if (isRunning.value) return

  isRunning.value = true
  hasError.value = false
  const safeConsole = createSafeConsole()

  try {
    // 清空之前的输出
    output.value = ''
    await nextTick()

    // 模拟Java代码到JavaScript的转换
    let jsCode = code.value

    // 简单的Java到JavaScript转换
    jsCode = jsCode
      .replace(/String\s+(\w+)\s*=/g, 'let $1 =')
      .replace(/System\.out\.println\(/g, 'console.log(')
      .replace(/\.equals\(/g, ' === ')
      .replace(/\\n/g, '\\n')

    // 创建一个沙盒环境
    const sandbox = {
      console: safeConsole,
      String: String,
      JSON: JSON,
      Object: Object,
      Array: Array,
      // 模拟Java的一些方法
      oldWay: '',
      newWay: '',
    }

    // 使用Function构造器在沙盒中执行代码
    const func = new Function(
      ...Object.keys(sandbox),
      `
      try {
        ${jsCode}
      } catch (error) {
        console.log('执行出错: ' + error.message);
      }
      `,
    )

    // 执行代码
    func(...Object.values(sandbox))

    // 获取输出
    const logs = safeConsole.getLogs()
    if (logs.length > 0) {
      output.value = logs.join('\n')
    } else {
      output.value = '代码执行完成，但没有输出内容。'
    }
  } catch (error) {
    hasError.value = true
    output.value = `❌ 执行错误: ${(error as Error).message}\n\n💡 提示：请检查代码语法是否正确。`
  } finally {
    isRunning.value = false
  }
}

const resetCode = () => {
  code.value = props.initialCode
  output.value = ''
  hasError.value = false
}

// 格式化代码
const formatCode = () => {
  // 简单的代码格式化
  let formatted = code.value
  formatted = formatted
    .split('\n')
    .map((line) => line.trim())
    .join('\n')
    .replace(/;\s*\n/g, ';\n')
    .replace(/{\s*\n/g, '{\n  ')
    .replace(/}\s*\n/g, '\n}\n')

  code.value = formatted
}
</script>

<style scoped>
.code-playground {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.playground-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-content {
  flex: 1;
}

.playground-title {
  color: #333;
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.playground-description {
  color: #666;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #bbb;
  transform: translateY(-1px);
}

.format-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.reset-btn:hover {
  background: #fce4ec;
  border-color: #e91e63;
  color: #c2185b;
}

.playground-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.section-title {
  color: #333;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
}

.section-hint {
  color: #999;
  font-size: 0.8rem;
  font-style: italic;
}

.code-editor {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.code-textarea {
  width: 100%;
  height: 300px;
  padding: 1rem;
  border: none;
  background: transparent;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #333;
  resize: none;
  outline: none;
}

.editor-footer {
  background: #e9ecef;
  padding: 0.75rem 1rem;
  border-top: 1px solid #dee2e6;
}

.run-btn {
  padding: 0.6rem 1.5rem;
  border: none;
  border-radius: 6px;
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.run-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #45a049, #4caf50);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.run-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.run-btn.running {
  background: linear-gradient(45deg, #ff9800, #f57c00);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.output-container {
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333;
}

.output-content {
  width: 100%;
  height: 300px;
  padding: 1rem;
  margin: 0;
  background: transparent;
  color: #d4d4d4;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.output-content.empty {
  color: #888;
  font-style: italic;
}

.output-content.has-error {
  color: #ff6b6b;
}

.playground-tips {
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 10px;
  padding: 1.5rem;
  border: 1px solid #e3f2fd;
}

.tips-title {
  color: #1976d2;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.tips-list {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
  padding-left: 1.2rem;
}

.tips-list li {
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .code-playground {
    padding: 1rem;
  }

  .playground-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    align-self: stretch;
  }

  .action-btn {
    flex: 1;
  }

  .playground-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .code-textarea,
  .output-content {
    height: 250px;
  }

  .playground-title {
    font-size: 1.3rem;
  }

  .tips-list {
    font-size: 0.85rem;
  }
}

/* 代码高亮样式（简单版） */
.code-textarea {
  tab-size: 2;
}

.code-textarea:focus {
  background: #ffffff;
  border: 2px solid #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 滚动条样式 */
.output-content::-webkit-scrollbar {
  width: 8px;
}

.output-content::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.output-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.output-content::-webkit-scrollbar-thumb:hover {
  background: #777;
}
</style>
