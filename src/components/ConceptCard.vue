<template>
  <div class="concept-card" @click="handleClick">
    <div class="card-front" :class="{ flipped: isRevealed }">
      <div class="card-title">{{ title }}</div>
      <div class="card-icon">🔍</div>
      <div class="click-hint">点击探索</div>
    </div>

    <div class="card-back" :class="{ revealed: isRevealed }">
      <div class="card-description">
        {{ description }}
      </div>
      <div class="back-to-front" @click.stop="handleClick">
        <span>点击收起</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  title: string
  description: string
  isRevealed: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'toggle'): void
}>()

const handleClick = () => {
  emit('toggle')
}
</script>

<style scoped>
.concept-card {
  position: relative;
  height: 250px;
  cursor: pointer;
  perspective: 1000px;
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.concept-card:hover {
  transform: translateY(-5px);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.6s ease;
}

.card-front {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  transform: rotateY(0deg);
}

.card-front.flipped {
  transform: rotateY(-180deg);
}

.card-back {
  background: white;
  color: #333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem;
  transform: rotateY(180deg);
  border: 2px solid #667eea;
}

.card-back.revealed {
  transform: rotateY(0deg);
}

.card-title {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.card-icon {
  font-size: 2.5rem;
  margin: 1rem 0;
  opacity: 0.8;
}

.click-hint {
  font-size: 0.9rem;
  opacity: 0.8;
  font-style: italic;
}

.card-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

.back-to-front {
  text-align: center;
  color: #667eea;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 5px;
  transition: background-color 0.2s ease;
}

.back-to-front:hover {
  background-color: #f0f0f0;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.concept-card:hover .card-front:not(.flipped) {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .concept-card {
    height: 200px;
  }

  .card-front,
  .card-back {
    padding: 1.5rem;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .card-icon {
    font-size: 2rem;
  }

  .card-description {
    font-size: 0.9rem;
  }
}
</style>
