<template>
  <div class="animation-container">
    <div class="tabs">
      <button :class="{ active: currentView === 'classpath' }" @click="currentView = 'classpath'">
        旧时代：类路径 (Classpath)
      </button>
      <button :class="{ active: currentView === 'modulepath' }" @click="currentView = 'modulepath'">
        新时代：模块路径 (Module Path)
      </button>
    </div>

    <div class="animation-area">
      <transition name="fade" mode="out-in">
        <div v-if="currentView === 'classpath'" class="scenario">
          <div class="explanation">
            <h4>场景：脆弱的"随缘"加载</h4>
            <p :class="{ highlight: classpathStep === 0 }">1. JVM 需要一个 `Logger` 类。</p>
            <p :class="{ highlight: classpathStep === 1 }">
              2. 它扫描类路径，首先在 `lib-v1.0.jar` 中找到了！
            </p>
            <p :class="{ highlight: classpathStep === 2 }">
              3. 加载成功！但...这是个旧版本，可能会导致运行时诡异的错误。
            </p>
            <p :class="{ highlight: classpathStep === 3 }">
              4. 那个更新、更正确的 `lib-v2.0.jar`
              因为排在后面，被完全忽略了。这就是"第一个获胜"的脆弱性。
            </p>
          </div>
          <div class="svg-container">
            <!-- Classpath SVG -->
            <svg viewBox="0 0 400 200" class="diagram">
              <!-- JVM -->
              <g>
                <rect x="10" y="80" width="60" height="40" fill="#f0ad4e" rx="5"></rect>
                <text x="40" y="105" text-anchor="middle">JVM</text>
              </g>

              <!-- Arrows -->
              <path
                :d="classpathArrowPath"
                stroke="#d9534f"
                stroke-width="2"
                fill="none"
                stroke-dasharray="4"
                class="arrow"
              ></path>

              <!-- Jars on Classpath -->
              <g class="jar" :class="{ 'jar-found': classpathStep >= 1 }">
                <rect
                  x="120"
                  y="20"
                  width="100"
                  height="40"
                  fill="#f5f5f5"
                  stroke="#ccc"
                  rx="3"
                ></rect>
                <text x="170" y="45" text-anchor="middle">lib-v1.0.jar</text>
                <text x="170" y="70" text-anchor="middle" font-size="10">(含 Logger v1)</text>
              </g>
              <g class="jar">
                <rect
                  x="120"
                  y="140"
                  width="100"
                  height="40"
                  fill="#f5f5f5"
                  stroke="#ccc"
                  rx="3"
                ></rect>
                <text x="170" y="165" text-anchor="middle">lib-v2.0.jar</text>
                <text x="170" y="190" text-anchor="middle" font-size="10">(含 Logger v2)</text>
              </g>

              <!-- Search path line -->
              <path
                d="M75 100 L 120 100 L 120 40"
                stroke="#ccc"
                stroke-width="1"
                fill="none"
                stroke-dasharray="2"
              ></path>
              <path
                d="M120 100 L 120 160"
                stroke="#ccc"
                stroke-width="1"
                fill="none"
                stroke-dasharray="2"
              ></path>
            </svg>
          </div>
        </div>

        <div v-else class="scenario">
          <div class="explanation">
            <h4>场景：可靠的"依赖图"加载</h4>
            <p :class="{ highlight: modulepathStep === 0 }">
              1. JVM 不再直接找类，而是先读取所有模块的"说明书" (`module-info.java`)。
            </p>
            <p :class="{ highlight: modulepathStep === 1 }">
              2. 它在内存中构建了一个依赖关系图。它清楚地知道 `my-app` 依赖 `lib-v2.0`。
            </p>
            <p :class="{ highlight: modulepathStep === 2 }">
              3. `lib-v1.0` 因为没有被任何模块依赖，所以被完全无视了。不存在版本冲突！
            </p>
            <p :class="{ highlight: modulepathStep === 3 }">
              4. JVM 精准地从 `lib-v2.0` 中加载 `Logger` 类。可靠、可预测！
            </p>
          </div>
          <div class="svg-container">
            <!-- Modulepath SVG -->
            <svg viewBox="0 0 400 200" class="diagram">
              <!-- JVM -->
              <g>
                <rect x="10" y="80" width="60" height="40" fill="#5cb85c" rx="5"></rect>
                <text x="40" y="105" text-anchor="middle">JVM</text>
              </g>

              <!-- Modules -->
              <g class="jar" :class="{ 'jar-scanned': modulepathStep >= 0 }">
                <rect
                  x="150"
                  y="20"
                  width="100"
                  height="40"
                  fill="#e7f3ff"
                  stroke="#007acc"
                  rx="3"
                ></rect>
                <text x="200" y="45" text-anchor="middle">my-app</text>
                <circle
                  cx="240"
                  cy="30"
                  r="8"
                  fill="#fff"
                  stroke="#007acc"
                  v-if="modulepathStep >= 1"
                  class="info-bubble"
                />
                <text
                  x="240"
                  y="33"
                  r="8"
                  fill="#007acc"
                  text-anchor="middle"
                  font-size="10"
                  v-if="modulepathStep >= 1"
                  class="info-bubble"
                >
                  i
                </text>
              </g>
              <g class="jar" :class="{ 'jar-ignored': modulepathStep >= 2 }">
                <rect
                  x="280"
                  y="80"
                  width="100"
                  height="40"
                  fill="#f5f5f5"
                  stroke="#ccc"
                  rx="3"
                ></rect>
                <text x="330" y="105" text-anchor="middle">lib-v1.0</text>
              </g>
              <g
                class="jar"
                :class="{ 'jar-linked': modulepathStep >= 2, 'jar-loaded': modulepathStep >= 3 }"
              >
                <rect
                  x="150"
                  y="140"
                  width="100"
                  height="40"
                  fill="#e7f3ff"
                  stroke="#007acc"
                  rx="3"
                ></rect>
                <text x="200" y="165" text-anchor="middle">lib-v2.0</text>
                <circle
                  cx="240"
                  cy="150"
                  r="8"
                  fill="#fff"
                  stroke="#007acc"
                  v-if="modulepathStep >= 1"
                  class="info-bubble"
                />
                <text
                  x="240"
                  y="153"
                  r="8"
                  fill="#007acc"
                  text-anchor="middle"
                  font-size="10"
                  v-if="modulepathStep >= 1"
                  class="info-bubble"
                >
                  i
                </text>
              </g>

              <!-- Dependency line -->
              <path
                d="M 200 60 L 200 140"
                stroke="#007acc"
                stroke-width="2"
                fill="none"
                class="arrow"
                v-if="modulepathStep >= 1"
              />
              <text x="210" y="100" fill="#007acc" font-size="10" v-if="modulepathStep >= 1">
                requires
              </text>

              <!-- Reading infos -->
              <path
                d="M75 100 Q 110 50, 150 40"
                stroke="#aaa"
                stroke-width="1.5"
                fill="none"
                stroke-dasharray="3"
                v-if="modulepathStep === 0"
                class="arrow"
              />
              <path
                d="M75 100 Q 110 150, 150 160"
                stroke="#aaa"
                stroke-width="1.5"
                fill="none"
                stroke-dasharray="3"
                v-if="modulepathStep === 0"
                class="arrow"
              />
            </svg>
          </div>
        </div>
      </transition>
    </div>
    <div class="controls">
      <button @click="prevStep" :disabled="isPrevDisabled">上一步</button>
      <button @click="nextStep" :disabled="isNextDisabled">下一步</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

const currentView = ref('classpath')
const classpathStep = ref(0)
const modulepathStep = ref(0)

const maxSteps = computed(() => (currentView.value === 'classpath' ? 3 : 3))
const currentStep = computed({
  get: () => (currentView.value === 'classpath' ? classpathStep.value : modulepathStep.value),
  set: (val) => {
    if (currentView.value === 'classpath') {
      classpathStep.value = val
    } else {
      modulepathStep.value = val
    }
  },
})

const isPrevDisabled = computed(() => currentStep.value === 0)
const isNextDisabled = computed(() => currentStep.value === maxSteps.value)

function nextStep() {
  if (currentStep.value < maxSteps.value) {
    currentStep.value++
  }
}

function prevStep() {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

watch(currentView, () => {
  classpathStep.value = 0
  modulepathStep.value = 0
})

const classpathArrowPath = computed(() => {
  if (classpathStep.value === 0) return 'M 75 100 L 115 100'
  if (classpathStep.value >= 1) return 'M 75 100 L 120 100 L 120 60'
  return ''
})
</script>

<style scoped>
.animation-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.tabs {
  display: flex;
  background-color: #f0f0f0;
}

.tabs button {
  flex: 1;
  padding: 12px;
  border: none;
  background: #f0f0f0;
  cursor: pointer;
  font-size: 1.1em;
  transition:
    background-color 0.3s,
    color 0.3s;
  border-bottom: 3px solid transparent;
}

.tabs button.active {
  background-color: #fff;
  border-bottom: 3px solid #007acc;
  font-weight: bold;
  color: #005a9e;
}

.animation-area {
  padding: 20px;
  background-color: #fff;
}

.scenario {
  display: flex;
  gap: 20px;
  align-items: center;
}

.explanation {
  flex-basis: 45%;
}
.svg-container {
  flex-basis: 55%;
  min-height: 220px;
}

.explanation h4 {
  margin-top: 0;
  color: #005a9e;
}

.explanation p {
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  margin: 5px 0;
}

.explanation .highlight {
  background-color: #fffbe6;
  font-weight: bold;
}

.diagram .jar {
  transition: all 0.5s ease;
}
.diagram .jar-found {
  stroke-width: 2;
  stroke: #d9534f;
}
.diagram .jar-linked {
  stroke-width: 2;
  stroke: #5cb85c;
}
.diagram .jar-loaded rect {
  fill: #dff0d8;
}

.diagram .jar-scanned rect {
  stroke-width: 1.5;
}

.diagram .jar-ignored {
  opacity: 0.4;
}

.diagram .arrow {
  transition: all 0.5s ease;
  animation: dash 2s linear infinite;
}

.diagram .info-bubble {
  animation: pop 0.3s ease-out;
}

@keyframes pop {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes dash {
  to {
    stroke-dashoffset: -16;
  }
}

.controls {
  display: flex;
  justify-content: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-top: 1px solid #ddd;
}
.controls button {
  padding: 8px 20px;
  margin: 0 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #fff;
  cursor: pointer;
  transition: background-color 0.2s;
}
.controls button:hover:not(:disabled) {
  background-color: #e9e9e9;
}
.controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
