<template>
  <div class="chapter1">
    <!-- 章节标题 -->
    <div class="chapter-header">
      <div class="container">
        <h1 class="chapter-title">
          <span class="chapter-number">第1章</span>
          Introducing Modern Java
        </h1>
        <p class="chapter-subtitle">深度学习现代Java的核心概念与演进机制</p>

        <!-- 进度指示器 -->
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <span class="progress-text">{{ progress }}% 完成</span>
        </div>
      </div>
    </div>

    <!-- 课程大纲 -->
    <div class="course-outline">
      <div class="container">
        <h2>📋 课程大纲</h2>
        <div class="outline-grid">
          <div
            v-for="(topic, index) in courseTopics"
            :key="index"
            class="outline-item"
            :class="{ active: currentTopic === index, completed: index < currentTopic }"
            @click="scrollToTopic(index)"
          >
            <div class="outline-number">{{ index + 1 }}</div>
            <div class="outline-content">
              <h3>{{ topic.title }}</h3>
              <p>{{ topic.description }}</p>
            </div>
            <div class="outline-status">
              <span v-if="index < currentTopic" class="status-icon completed">✓</span>
              <span v-else-if="index === currentTopic" class="status-icon current">📖</span>
              <span v-else class="status-icon pending">⏳</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="container">
        <div class="content-layout">
          <!-- 侧边导航 -->
          <aside class="sidebar">
            <div class="sidebar-sticky">
              <h3>本章导航</h3>
              <nav class="topic-nav">
                <a
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  :href="`#topic-${index}`"
                  class="nav-link"
                  :class="{ active: currentTopic === index }"
                  @click="setCurrentTopic(index)"
                >
                  {{ topic.title }}
                </a>
              </nav>

              <!-- 学习工具 -->
              <div class="learning-tools">
                <h4>学习工具</h4>
                <button @click="toggleNotes" class="tool-button">📝 笔记</button>
                <button @click="toggleHighlights" class="tool-button">🖍️ 高亮</button>
                <button @click="showQuiz" class="tool-button">🧠 测验</button>
              </div>
            </div>
          </aside>

          <!-- 主内容 -->
          <main class="content-main">
            <!-- Topic 1: Java语言 vs Java平台 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ConceptCard
                title="Java 语言 vs. Java 平台"
                :concept-data="javaLanguagePlatformData"
                @interaction="handleInteraction"
              />

              <JavaCompilationAnimation />

              <div class="deep-dive">
                <h3>🔍 深度解读</h3>
                <div class="explanation-tabs">
                  <div class="tab-buttons">
                    <button
                      v-for="tab in explanationTabs"
                      :key="tab.id"
                      @click="activeTab = tab.id"
                      :class="{ active: activeTab === tab.id }"
                      class="tab-button"
                    >
                      {{ tab.label }}
                    </button>
                  </div>
                  <div class="tab-content">
                    <div v-if="activeTab === 'definition'" class="tab-panel">
                      <h4>概念定义</h4>
                      <div class="definition-cards">
                        <div class="definition-card language">
                          <h5>🔤 Java 语言</h5>
                          <p>一种静态类型、面向对象的编程语言，源代码是人类可读的（.java文件）</p>
                          <div class="analogy">
                            <strong>类比：</strong>就像"普通话"，是我们与计算机沟通的语法规则和词汇
                          </div>
                        </div>
                        <div class="definition-card platform">
                          <h5>⚙️ Java 平台</h5>
                          <p>提供Java程序运行环境的软件集合，核心是JVM，负责执行编译后的字节码</p>
                          <div class="analogy">
                            <strong>类比：</strong>像"同声传译系统+执行团队"，将代码翻译并执行
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-if="activeTab === 'principles'" class="tab-panel">
                      <h4>第一性原理</h4>
                      <div class="principles-list">
                        <div class="principle-item">
                          <h5>🎯 解耦与跨平台</h5>
                          <p>
                            通过引入"平台"中间层，Java语言与底层操作系统解耦，实现"一次编写，到处运行"
                          </p>
                        </div>
                        <div class="principle-item">
                          <h5>🌐 生态系统多样性</h5>
                          <p>
                            JVM规范公开，任何语言只要编译成.class文件就能运行，催生了Kotlin、Scala等JVM语言
                          </p>
                        </div>
                        <div class="principle-item">
                          <h5>📦 .class文件契约</h5>
                          <p>语言和平台间的唯一契约是.class文件格式，就像标准化的集装箱</p>
                        </div>
                      </div>
                    </div>

                    <div v-if="activeTab === 'examples'" class="tab-panel">
                      <h4>实例与类比</h4>
                      <ProcessAnimation :steps="compilationSteps" title="Java编译执行流程" />
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Topic 2: 新的Java发布模型 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ConceptCard
                title="新的 Java 发布模型"
                :concept-data="releaseModelData"
                @interaction="handleInteraction"
              />

              <div class="release-timeline">
                <h3>📅 Java发布时间线</h3>
                <div class="timeline-container">
                  <div class="timeline">
                    <div
                      v-for="release in javaReleases"
                      :key="release.version"
                      class="timeline-item"
                      :class="{ lts: release.isLTS }"
                    >
                      <div class="timeline-marker"></div>
                      <div class="timeline-content">
                        <h4>Java {{ release.version }}</h4>
                        <p class="release-date">{{ release.date }}</p>
                        <p class="release-features">{{ release.features }}</p>
                        <span v-if="release.isLTS" class="lts-badge">LTS</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Topic 3: var关键字 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ConceptCard
                title="增强的类型推断 (var 关键字)"
                :concept-data="varKeywordData"
                @interaction="handleInteraction"
              />

              <CodePlayground
                :examples="varExamples"
                title="var 关键字实践"
                @code-run="handleCodeRun"
              />
            </section>

            <!-- Topic 4: 语言与平台演进机制 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ConceptCard
                title="语言与平台的演进机制"
                :concept-data="evolutionMechanismData"
                @interaction="handleInteraction"
              />

              <div class="evolution-mechanisms">
                <h3>🔧 演进机制详解</h3>
                <div class="mechanisms-grid">
                  <div class="mechanism-card syntactic-sugar">
                    <div class="mechanism-header">
                      <span class="mechanism-icon">🍬</span>
                      <h4>语法糖 (Syntactic Sugar)</h4>
                      <span class="cost-badge low">成本最低</span>
                    </div>
                    <div class="mechanism-content">
                      <p><strong>定义：</strong>编译器层面的语法简化，不涉及JVM变更</p>
                      <p><strong>示例：</strong>try-with-resources, enhanced for loop</p>
                      <p><strong>特点：</strong>编译时"脱糖"，转换为基础语法</p>
                    </div>
                  </div>

                  <div class="mechanism-card jeps-jsrs">
                    <div class="mechanism-header">
                      <span class="mechanism-icon">📋</span>
                      <h4>JEPs & JSRs</h4>
                      <span class="cost-badge medium">成本中等</span>
                    </div>
                    <div class="mechanism-content">
                      <p><strong>JSR：</strong>Java规范请求，流程较重，适合成熟技术</p>
                      <p><strong>JEP：</strong>JDK增强提案，轻量级，快速推动变更</p>
                      <p><strong>关系：</strong>大JSR通常由多个小JEP组成</p>
                    </div>
                  </div>

                  <div class="mechanism-card preview-features">
                    <div class="mechanism-header">
                      <span class="mechanism-icon">🧪</span>
                      <h4>预览特性 (Preview)</h4>
                      <span class="cost-badge high">风险较高</span>
                    </div>
                    <div class="mechanism-content">
                      <p><strong>目的：</strong>收集社区反馈，验证设计</p>
                      <p><strong>使用：</strong>需要--enable-preview标志</p>
                      <p><strong>警告：</strong>绝不可用于生产环境！</p>
                    </div>
                  </div>

                  <div class="mechanism-card incubating-features">
                    <div class="mechanism-header">
                      <span class="mechanism-icon">🥚</span>
                      <h4>孵化特性 (Incubating)</h4>
                      <span class="cost-badge medium">成本中等</span>
                    </div>
                    <div class="mechanism-content">
                      <p><strong>范围：</strong>主要是新的API模块</p>
                      <p><strong>位置：</strong>jdk.incubator包中</p>
                      <p><strong>示例：</strong>HTTP/2客户端最初就是孵化特性</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="cost-complexity-chart">
                <h3>📊 变更成本与复杂度对比</h3>
                <div class="chart-container">
                  <div class="chart-bars">
                    <div class="chart-bar" data-label="库方法">
                      <div class="bar-fill" style="height: 20%"></div>
                      <span class="bar-label">库方法</span>
                    </div>
                    <div class="chart-bar" data-label="语法糖">
                      <div class="bar-fill" style="height: 35%"></div>
                      <span class="bar-label">语法糖</span>
                    </div>
                    <div class="chart-bar" data-label="语言特性">
                      <div class="bar-fill" style="height: 60%"></div>
                      <span class="bar-label">语言特性</span>
                    </div>
                    <div class="chart-bar" data-label="类文件格式">
                      <div class="bar-fill" style="height: 80%"></div>
                      <span class="bar-label">类文件格式</span>
                    </div>
                    <div class="chart-bar" data-label="VM特性">
                      <div class="bar-fill" style="height: 100%"></div>
                      <span class="bar-label">VM特性</span>
                    </div>
                  </div>
                  <div class="chart-axis">
                    <span>实现成本与风险</span>
                  </div>
                </div>
              </div>
            </section>

            <!-- Topic 5: Java 11 重要变更 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ConceptCard
                title="Java 11 的重要小型变更"
                :concept-data="java11ChangesData"
                @interaction="handleInteraction"
              />

              <div class="java11-features">
                <h3>🚀 Java 11 核心新特性</h3>

                <!-- 集合工厂 -->
                <div class="feature-detail">
                  <div class="feature-header">
                    <span class="feature-icon">📦</span>
                    <h4>集合工厂 (JEP 213)</h4>
                    <span class="jep-badge">JEP 213</span>
                  </div>
                  <div class="feature-content">
                    <div class="feature-description">
                      <p>通过静态工厂方法创建不可变集合，大大简化了小型集合的创建过程。</p>
                    </div>
                    <CodePlayground
                      :examples="collectionFactoryExamples"
                      title="集合工厂实践"
                      @code-run="handleCodeRun"
                    />
                  </div>
                </div>

                <!-- HTTP/2 客户端 -->
                <div class="feature-detail">
                  <div class="feature-header">
                    <span class="feature-icon">🌐</span>
                    <h4>HTTP/2 客户端 (JEP 321)</h4>
                    <span class="jep-badge">JEP 321</span>
                  </div>
                  <div class="feature-content">
                    <div class="feature-description">
                      <p>全新的现代化HTTP客户端，原生支持HTTP/2，提供同步和异步API。</p>
                    </div>
                    <div class="http-comparison">
                      <div class="comparison-item">
                        <h5>🐌 HTTP/1.1 - 队头阻塞</h5>
                        <div class="http1-demo">
                          <div class="request-sequence">
                            <div class="request-item">HTML请求</div>
                            <div class="arrow">→</div>
                            <div class="response-item">HTML响应</div>
                            <div class="arrow">→</div>
                            <div class="request-item">图片请求</div>
                            <div class="arrow">→</div>
                            <div class="response-item">图片响应</div>
                          </div>
                          <p class="demo-note">必须等待前一个请求完成</p>
                        </div>
                      </div>
                      <div class="comparison-item">
                        <h5>⚡ HTTP/2 - 多路复用</h5>
                        <div class="http2-demo">
                          <div class="multiplexed-streams">
                            <div class="stream">
                              <span class="stream-label">Stream 1:</span>
                              <div class="frame">HTML</div>
                              <div class="frame">Frame</div>
                              <div class="frame">Frame</div>
                            </div>
                            <div class="stream">
                              <span class="stream-label">Stream 2:</span>
                              <div class="frame">IMG</div>
                              <div class="frame">Frame</div>
                            </div>
                          </div>
                          <p class="demo-note">并行处理多个请求</p>
                        </div>
                      </div>
                    </div>
                    <CodePlayground
                      :examples="httpClientExamples"
                      title="HTTP/2 客户端实践"
                      @code-run="handleCodeRun"
                    />
                  </div>
                </div>

                <!-- 单文件程序 -->
                <div class="feature-detail">
                  <div class="feature-header">
                    <span class="feature-icon">📄</span>
                    <h4>单文件源码程序 (JEP 330)</h4>
                    <span class="jep-badge">JEP 330</span>
                  </div>
                  <div class="feature-content">
                    <div class="feature-description">
                      <p>可以直接运行单个Java源文件，无需先编译，极大简化了小程序和脚本的开发。</p>
                    </div>
                    <div class="single-file-demo">
                      <div class="demo-steps">
                        <div class="step">
                          <span class="step-number">1</span>
                          <div class="step-content">
                            <h5>创建 HelloWorld.java</h5>
                            <pre class="code-snippet">
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}</pre
                            >
                          </div>
                        </div>
                        <div class="step">
                          <span class="step-number">2</span>
                          <div class="step-content">
                            <h5>直接运行（无需编译）</h5>
                            <pre class="command">
$ java HelloWorld.java
Hello, World!</pre
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 移除EE模块 -->
                <div class="feature-detail">
                  <div class="feature-header">
                    <span class="feature-icon">🗑️</span>
                    <h4>移除 Java EE 模块 (JEP 320)</h4>
                    <span class="jep-badge">JEP 320</span>
                  </div>
                  <div class="feature-content">
                    <div class="feature-description">
                      <p>从JDK中移除了JAXB、JAX-WS、CORBA等模块，使核心JDK更加轻量。</p>
                    </div>
                    <div class="removal-impact">
                      <div class="removed-modules">
                        <h5>被移除的模块：</h5>
                        <ul>
                          <li>JAXB (XML绑定)</li>
                          <li>JAX-WS (Web服务)</li>
                          <li>CORBA (分布式对象)</li>
                          <li>Java Activation Framework</li>
                        </ul>
                      </div>
                      <div class="migration-guide">
                        <h5>迁移指南：</h5>
                        <p>如果您的项目使用了这些模块，需要添加相应的第三方依赖。</p>
                        <pre class="migration-example">
&lt;dependency&gt;
    &lt;groupId&gt;javax.xml.bind&lt;/groupId&gt;
    &lt;artifactId&gt;jaxb-api&lt;/artifactId&gt;
    &lt;version&gt;2.3.1&lt;/version&gt;
&lt;/dependency&gt;</pre
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 笔记面板 -->
    <div v-if="showNotes" class="notes-panel">
      <div class="notes-header">
        <h3>📝 我的笔记</h3>
        <button @click="toggleNotes" class="close-button">×</button>
      </div>
      <div class="notes-content">
        <textarea
          v-model="userNotes"
          placeholder="在这里记录你的学习笔记..."
          class="notes-textarea"
        ></textarea>
      </div>
    </div>

    <!-- 测验模态框 -->
    <div v-if="showQuizModal" class="quiz-modal">
      <div class="quiz-content">
        <h3>🧠 知识测验</h3>
        <div class="quiz-question">
          <p>{{ currentQuiz.question }}</p>
          <div class="quiz-options">
            <button
              v-for="(option, index) in currentQuiz.options"
              :key="index"
              @click="selectAnswer(index)"
              class="quiz-option"
              :class="{ selected: selectedAnswer === index }"
            >
              {{ option }}
            </button>
          </div>
        </div>
        <div class="quiz-actions">
          <button @click="submitAnswer" class="submit-button">提交答案</button>
          <button @click="showQuizModal = false" class="cancel-button">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ConceptCard from '@/components/ConceptCard.vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import JavaCompilationAnimation from '@/components/JavaCompilationAnimation.vue'
import ProcessAnimation from '@/components/ProcessAnimation.vue'
import CodePlayground from '@/components/CodePlayground.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const activeTab = ref('definition')
const showNotes = ref(false)
const showQuizModal = ref(false)
const userNotes = ref('')
const selectedAnswer = ref(-1)

// 课程主题
const courseTopics = [
  {
    title: 'Java 语言 vs. Java 平台',
    description: '理解语言与平台的根本区别，掌握跨平台原理',
  },
  {
    title: '新的 Java 发布模型',
    description: '了解时间驱动的发布周期和LTS版本策略',
  },
  {
    title: '增强的类型推断 (var)',
    description: '掌握var关键字的使用和局限性',
  },
  {
    title: '语言与平台演进机制',
    description: '学习语法糖、JEPs、孵化和预览特性',
  },
  {
    title: 'Java 11 重要变更',
    description: '探索集合工厂、HTTP/2客户端等新特性',
  },
]

// 解释标签页
const explanationTabs = [
  { id: 'definition', label: '概念定义' },
  { id: 'principles', label: '第一性原理' },
  { id: 'examples', label: '实例与类比' },
]

// Java语言vs平台数据
const javaLanguagePlatformData = {
  title: 'Java 语言 vs. Java 平台',
  keyPoints: [
    '语言：静态类型、面向对象的编程语言（.java文件）',
    '平台：提供运行环境的软件集合，核心是JVM（.class文件）',
    '契约：.class文件格式是语言和平台间的唯一桥梁',
    '执行：编译+解释+JIT的动态编译系统',
  ],
  interactiveElements: [
    { type: 'animation', label: '编译流程动画' },
    { type: 'comparison', label: '语言对比表' },
  ],
}

// 发布模型数据
const releaseModelData = {
  title: '新的 Java 发布模型',
  keyPoints: [
    '特性发布：每6个月发布一个新版本',
    'LTS版本：每2-3年指定一个长期支持版本',
    '时间驱动：不再等待重大特性，按时间表发布',
    '主线开发：特性分支开发，主干定期发布',
  ],
}

// var关键字数据
const varKeywordData = {
  title: '增强的类型推断 (var 关键字)',
  keyPoints: [
    '局部变量类型推断：编译器自动推断变量类型',
    '静态类型：var不是动态类型，编译时确定类型',
    '减少冗余：简化复杂泛型类型的声明',
    '局限性：仅限局部变量，需要初始化',
  ],
}

// Java发布时间线
const javaReleases = [
  { version: '8', date: '2014年3月', features: 'Lambda表达式、Stream API', isLTS: true },
  { version: '9', date: '2017年9月', features: '模块系统、集合工厂', isLTS: false },
  { version: '10', date: '2018年3月', features: 'var关键字、垃圾收集器改进', isLTS: false },
  { version: '11', date: '2018年9月', features: 'HTTP/2客户端、单文件程序', isLTS: true },
  { version: '12', date: '2019年3月', features: 'Switch表达式预览', isLTS: false },
  { version: '13', date: '2019年9月', features: 'Text Blocks预览', isLTS: false },
  { version: '14', date: '2020年3月', features: 'Records预览、模式匹配', isLTS: false },
  { version: '15', date: '2020年9月', features: 'Sealed Classes预览', isLTS: false },
  { version: '16', date: '2021年3月', features: 'Records正式版', isLTS: false },
  { version: '17', date: '2021年9月', features: 'Sealed Classes正式版', isLTS: true },
]

// 编译步骤
const compilationSteps = [
  {
    title: '.java文件',
    description: '人类可读的Java源代码',
    icon: '📝',
    details: 'HelloWorld.java - 我们编写的源代码',
  },
  {
    title: 'javac编译器',
    description: '将源代码编译成字节码',
    icon: '⚙️',
    details: 'javac HelloWorld.java - 编译过程',
  },
  {
    title: '.class文件',
    description: 'JVM可执行的字节码',
    icon: '📦',
    details: 'HelloWorld.class - 平台无关的中间代码',
  },
  {
    title: 'JVM加载',
    description: '类加载器加载字节码',
    icon: '🔄',
    details: '类加载、链接、初始化过程',
  },
  {
    title: '解释执行',
    description: '解释器快速启动程序',
    icon: '🚀',
    details: '逐行解释字节码，快速响应',
  },
  {
    title: 'JIT编译',
    description: '热点代码编译成机器码',
    icon: '⚡',
    details: '运行时优化，提升性能',
  },
]

// var关键字示例
const varExamples = [
  {
    title: '基本用法对比',
    code: `// 传统方式 - 冗余的类型声明
Map<String, List<String>> userChannels = new HashMap<String, List<String>>();
System.out.println("传统方式创建成功: " + userChannels.getClass().getSimpleName());

// Java 7 菱形语法 - 稍有改善
Map<String, List<String>> userChannels2 = new HashMap<>();
System.out.println("菱形语法创建成功: " + userChannels2.getClass().getSimpleName());

// Java 10+ var - 简洁明了
var userChannels3 = new HashMap<String, List<String>>();
System.out.println("var方式创建成功: " + userChannels3.getClass().getSimpleName());`,
    explanation: 'var关键字可以大大简化复杂类型的声明，让代码更加简洁易读',
  },
  {
    title: '类型推断演示',
    code: `// var 会自动推断类型
var message = "Hello, Java!";
System.out.println("字符串类型: " + message);

var number = 42;
System.out.println("整数类型: " + number);

var list = List.of("apple", "banana", "orange");
System.out.println("列表内容: " + list);

var map = Map.of("name", "Java", "version", "11");
System.out.println("映射内容: " + map);`,
    explanation: 'var会根据初始化表达式自动推断变量的类型，编译时就确定了具体类型',
  },
  {
    title: '使用限制示例',
    code: `// ✅ 正确用法
var validString = "这是有效的";
System.out.println("有效声明: " + validString);

var validList = new ArrayList<String>();
System.out.println("有效列表: " + validList);

// 以下是错误用法（注释掉避免编译错误）:
// var invalid; // ❌ 必须初始化
// var nullVar = null; // ❌ 无法推断null的类型
// var lambda = () -> {}; // ❌ Lambda需要目标类型

System.out.println("var使用限制演示完成");`,
    explanation: 'var有一些使用限制：必须初始化、不能用于null、Lambda表达式需要明确目标类型',
  },
]

// 语言与平台演进机制数据
const evolutionMechanismData = {
  definition: '语言与平台的演进机制',
  description: 'Java通过多种机制来推动语言和平台的演进，每种机制都有不同的成本和适用场景。',
  keyPoints: [
    '语法糖：编译器层面的简化，成本最低',
    'JEPs：轻量级增强提案，快速推动变更',
    'JSRs：重量级规范请求，适合成熟技术',
    '预览特性：收集反馈，验证设计',
    '孵化特性：新API的试验场',
  ],
  examples: [
    {
      title: '语法糖示例',
      before: 'for (int i = 0; i < list.size(); i++) { String item = list.get(i); }',
      after: 'for (String item : list) { }',
    },
    {
      title: 'JEP示例',
      before: 'Map<String, List<String>> map = new HashMap<>();',
      after: 'var map = new HashMap<String, List<String>>();',
    },
  ],
}

// Java 11 重要变更数据
const java11ChangesData = {
  definition: 'Java 11 的重要小型变更',
  description: 'Java 11虽然是LTS版本，但包含了许多重要的小型变更，这些变更显著提升了开发体验。',
  keyPoints: [
    '集合工厂：简化不可变集合创建',
    'HTTP/2客户端：现代化网络编程',
    '单文件程序：简化脚本开发',
    '移除EE模块：精简核心JDK',
    'String新方法：增强字符串处理',
  ],
  impact: '这些变更虽然看似微小，但对日常开发产生了深远影响，体现了Java"小步快跑"的演进策略。',
}

// 集合工厂示例
const collectionFactoryExamples = [
  {
    title: '创建不可变List',
    code: `// Java 8 之前的方式
List<String> oldWay = new ArrayList<>();
oldWay.add("apple");
oldWay.add("banana");
oldWay.add("cherry");
List<String> immutableOld = Collections.unmodifiableList(oldWay);
System.out.println("旧方式: " + immutableOld);

// Java 9+ 集合工厂
List<String> newWay = List.of("apple", "banana", "cherry");
System.out.println("新方式: " + newWay);

// 尝试修改会抛出异常
try {
    newWay.add("date");
} catch (UnsupportedOperationException e) {
    System.out.println("不可变集合，无法修改: " + e.getMessage());
}`,
    explanation: 'List.of()创建的是真正的不可变集合，比Collections.unmodifiableList()更安全高效',
  },
  {
    title: '创建不可变Set和Map',
    code: `// 创建不可变Set
Set<String> colors = Set.of("red", "green", "blue");
System.out.println("颜色集合: " + colors);

// 创建不可变Map
Map<String, Integer> scores = Map.of(
    "Alice", 95,
    "Bob", 87,
    "Charlie", 92
);
System.out.println("成绩映射: " + scores);

// Map.of 最多支持10个键值对
// 更多键值对使用 Map.ofEntries
Map<String, String> countries = Map.ofEntries(
    Map.entry("CN", "China"),
    Map.entry("US", "United States"),
    Map.entry("JP", "Japan"),
    Map.entry("UK", "United Kingdom")
);
System.out.println("国家代码: " + countries);`,
    explanation: 'Set.of()和Map.of()提供了创建小型不可变集合的便捷方法',
  },
  {
    title: '集合工厂的限制',
    code: `// ✅ 正确用法
List<String> validList = List.of("a", "b", "c");
System.out.println("有效列表: " + validList);

// ❌ 不能包含null
try {
    List<String> nullList = List.of("a", null, "c");
} catch (NullPointerException e) {
    System.out.println("集合工厂不允许null: " + e.getMessage());
}

// ❌ Set不能有重复元素
try {
    Set<String> duplicateSet = Set.of("a", "b", "a");
} catch (IllegalArgumentException e) {
    System.out.println("Set不能有重复元素: " + e.getMessage());
}

System.out.println("集合工厂使用注意事项演示完成");`,
    explanation: '集合工厂有一些限制：不允许null值，Set不允许重复元素',
  },
]

// HTTP客户端示例
const httpClientExamples = [
  {
    title: '同步HTTP请求',
    code: `// 创建HTTP客户端
HttpClient client = HttpClient.newHttpClient();

// 构建请求
HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.github.com/users/octocat"))
    .header("Accept", "application/json")
    .GET()
    .build();

// 发送同步请求
try {
    HttpResponse<String> response = client.send(request,
        HttpResponse.BodyHandlers.ofString());

    System.out.println("状态码: " + response.statusCode());
    System.out.println("响应头: " + response.headers().map());
    System.out.println("响应体长度: " + response.body().length());
} catch (Exception e) {
    System.out.println("请求失败: " + e.getMessage());
}`,
    explanation: '新的HTTP客户端提供了简洁的API来发送HTTP请求',
  },
  {
    title: '异步HTTP请求',
    code: `// 创建HTTP客户端
HttpClient client = HttpClient.newHttpClient();

// 构建请求
HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://httpbin.org/delay/2"))
    .timeout(Duration.ofSeconds(5))
    .GET()
    .build();

// 发送异步请求
CompletableFuture<HttpResponse<String>> future = client.sendAsync(request,
    HttpResponse.BodyHandlers.ofString());

// 处理响应
future.thenAccept(response -> {
    System.out.println("异步响应状态: " + response.statusCode());
}).exceptionally(throwable -> {
    System.out.println("异步请求异常: " + throwable.getMessage());
    return null;
});

System.out.println("异步请求已发送，继续执行其他任务...");`,
    explanation: 'HTTP客户端支持异步操作，提高了并发性能',
  },
  {
    title: 'HTTP/2特性演示',
    code: `// 创建支持HTTP/2的客户端
HttpClient client = HttpClient.newBuilder()
    .version(HttpClient.Version.HTTP_2)
    .connectTimeout(Duration.ofSeconds(10))
    .build();

// 创建多个请求
List<HttpRequest> requests = List.of(
    HttpRequest.newBuilder()
        .uri(URI.create("https://httpbin.org/json"))
        .GET().build(),
    HttpRequest.newBuilder()
        .uri(URI.create("https://httpbin.org/uuid"))
        .GET().build(),
    HttpRequest.newBuilder()
        .uri(URI.create("https://httpbin.org/ip"))
        .GET().build()
);

// 并发发送多个请求（HTTP/2多路复用）
List<CompletableFuture<HttpResponse<String>>> futures = requests.stream()
    .map(request -> client.sendAsync(request, HttpResponse.BodyHandlers.ofString()))
    .collect(Collectors.toList());

System.out.println("HTTP/2 多路复用演示：同时发送 " + futures.size() + " 个请求");`,
    explanation: 'HTTP/2的多路复用特性允许在单个连接上并发处理多个请求',
  },
]

// 测验题目
const currentQuiz = {
  question: 'Java的var关键字属于什么类型系统？',
  options: [
    '动态类型系统，类型可以在运行时改变',
    '静态类型系统，类型在编译时确定',
    '弱类型系统，不进行类型检查',
    '混合类型系统，既有静态又有动态特性',
  ],
  correctAnswer: 1,
  explanation: 'var关键字是静态类型推断，编译器在编译时就确定了变量的具体类型，运行时不可改变。',
}

// 方法
const scrollToTopic = (index: number) => {
  const element = document.getElementById(`topic-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    setCurrentTopic(index)
  }
}

const setCurrentTopic = (index: number) => {
  currentTopic.value = index
  updateProgress()
}

const updateProgress = () => {
  progress.value = Math.round(((currentTopic.value + 1) / courseTopics.length) * 100)
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const toggleHighlights = () => {
  // 实现高亮功能
  console.log('Toggle highlights')
}

const showQuiz = () => {
  showQuizModal.value = true
  selectedAnswer.value = -1
}

const selectAnswer = (index: number) => {
  selectedAnswer.value = index
}

const submitAnswer = () => {
  if (selectedAnswer.value === currentQuiz.correctAnswer) {
    alert('正确！' + currentQuiz.explanation)
  } else {
    alert(
      '错误。正确答案是：' +
        currentQuiz.options[currentQuiz.correctAnswer] +
        '\n\n' +
        currentQuiz.explanation,
    )
  }
  showQuizModal.value = false
}

const handleInteraction = (data: any) => {
  console.log('Interaction:', data)
  // 处理交互事件
}

const handleCodeRun = (result: any) => {
  console.log('Code run result:', result)
  // 处理代码运行结果
}

// 滚动监听
const handleScroll = () => {
  const sections = document.querySelectorAll('.topic-section')
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop

  sections.forEach((section, index) => {
    const element = section as HTMLElement
    const offsetTop = element.offsetTop - 100
    const offsetBottom = offsetTop + element.offsetHeight

    if (scrollTop >= offsetTop && scrollTop < offsetBottom) {
      setCurrentTopic(index)
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  updateProgress()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.chapter1 {
  min-height: 100vh;
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 章节头部 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.chapter-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-number {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 1rem;
  margin-right: 1rem;
  backdrop-filter: blur(10px);
}

.chapter-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

/* 进度条 */
.progress-container {
  max-width: 400px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 课程大纲 */
.course-outline {
  padding: 3rem 0;
  background: white;
}

.course-outline h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #333;
}

.outline-grid {
  display: grid;
  gap: 1.5rem;
}

.outline-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}

.outline-item:hover {
  background: #e3f2fd;
  border-left-color: #2196f3;
  transform: translateX(5px);
}

.outline-item.active {
  background: #e8f5e8;
  border-left-color: #4caf50;
}

.outline-item.completed {
  background: #f3e5f5;
  border-left-color: #9c27b0;
}

.outline-number {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 1rem;
  flex-shrink: 0;
}

.outline-content {
  flex: 1;
}

.outline-content h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.outline-status {
  margin-left: 1rem;
}

.status-icon {
  font-size: 1.5rem;
}

.status-icon.completed {
  color: #4caf50;
}

.status-icon.current {
  color: #ff9800;
}

.status-icon.pending {
  color: #9e9e9e;
}

/* 主内容布局 */
.main-content {
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

/* 侧边栏 */
.sidebar {
  position: sticky;
  top: 2rem;
}

.sidebar-sticky {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sidebar h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.topic-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.nav-link {
  padding: 0.75rem 1rem;
  color: #666;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.nav-link:hover {
  background: #f0f0f0;
  color: #333;
}

.nav-link.active {
  background: #e3f2fd;
  color: #1976d2;
  font-weight: 600;
}

.learning-tools h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.tool-button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.tool-button:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

/* 主内容区域 */
.content-main {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.topic-section {
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.topic-section:last-child {
  border-bottom: none;
}

/* 深度解读区域 */
.deep-dive {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.deep-dive h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.explanation-tabs {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-buttons {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-button {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tab-button:hover {
  background: #e9ecef;
  color: #333;
}

.tab-button.active {
  background: white;
  color: #667eea;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
}

.tab-content {
  padding: 2rem;
}

.tab-panel h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

/* 定义卡片 */
.definition-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.definition-card {
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.definition-card.language {
  background: #e8f5e8;
  border-left-color: #4caf50;
}

.definition-card.platform {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.definition-card h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.definition-card p {
  margin: 0 0 1rem 0;
  color: #666;
  line-height: 1.6;
}

.analogy {
  font-style: italic;
  color: #888;
  font-size: 0.9rem;
}

/* 原理列表 */
.principles-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.principle-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.principle-item h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.principle-item p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* 发布时间线 */
.release-timeline {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.release-timeline h3 {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 1.5rem;
  text-align: center;
}

.timeline-container {
  max-width: 800px;
  margin: 0 auto;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 3px #667eea;
}

.timeline-item.lts .timeline-marker {
  background: #4caf50;
  box-shadow: 0 0 0 3px #4caf50;
  width: 16px;
  height: 16px;
  top: 0.3rem;
  left: -2.2rem;
}

.timeline-content {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.timeline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.release-date {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.release-features {
  color: #555;
  margin: 0;
  line-height: 1.5;
}

.lts-badge {
  position: absolute;
  top: -8px;
  right: 1rem;
  background: #4caf50;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 笔记面板 */
.notes-panel {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  width: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.notes-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f0f0f0;
  color: #333;
}

.notes-content {
  padding: 1rem;
}

.notes-textarea {
  width: 100%;
  height: 200px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-family: inherit;
  font-size: 0.9rem;
  resize: vertical;
  outline: none;
  transition: border-color 0.2s ease;
}

.notes-textarea:focus {
  border-color: #667eea;
}

/* 测验模态框 */
.quiz-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.quiz-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quiz-content h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
  text-align: center;
}

.quiz-question p {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.1rem;
  line-height: 1.6;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.quiz-option {
  padding: 1rem;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  font-size: 0.95rem;
}

.quiz-option:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.quiz-option.selected {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.quiz-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.submit-button,
.cancel-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.submit-button {
  background: #4caf50;
  color: white;
}

.submit-button:hover {
  background: #45a049;
}

.cancel-button {
  background: #f0f0f0;
  color: #666;
}

.cancel-button:hover {
  background: #e0e0e0;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .notes-panel {
    position: fixed;
    right: 1rem;
    left: 1rem;
    width: auto;
    top: auto;
    bottom: 2rem;
    transform: none;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .chapter-number {
    display: block;
    margin: 0 0 1rem 0;
  }

  .definition-cards {
    grid-template-columns: 1fr;
  }

  .tab-buttons {
    flex-direction: column;
  }

  .timeline {
    padding-left: 1rem;
  }

  .timeline::before {
    left: 0.5rem;
  }

  .timeline-item {
    padding-left: 1.5rem;
  }

  .timeline-marker {
    left: -1.5rem;
  }

  .timeline-item.lts .timeline-marker {
    left: -1.7rem;
  }

  /* 演进机制响应式 */
  .mechanisms-grid {
    grid-template-columns: 1fr;
  }

  .http-comparison {
    grid-template-columns: 1fr;
  }

  .demo-steps {
    flex-direction: column;
    gap: 1rem;
  }

  .step {
    flex-direction: column;
    gap: 0.5rem;
  }

  .step-content {
    width: 100%;
  }

  .code-snippet,
  .command {
    font-size: 0.8rem;
    padding: 0.75rem;
  }

  .removal-impact {
    grid-template-columns: 1fr;
  }

  .chart-bars {
    flex-direction: column;
    height: auto;
    gap: 0.5rem;
  }

  .chart-bar {
    flex-direction: row;
    height: 40px;
  }

  .bar-fill {
    height: 100%;
    margin-bottom: 0;
    margin-right: 0.5rem;
  }

  .bar-label {
    writing-mode: horizontal-tb;
  }
}

/* 演进机制样式 */
.evolution-mechanisms {
  margin-top: 2rem;
}

.mechanisms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.mechanism-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: transform 0.3s ease;
}

.mechanism-card:hover {
  transform: translateY(-5px);
}

.mechanism-card.syntactic-sugar {
  border-left-color: #4caf50;
}

.mechanism-card.jeps-jsrs {
  border-left-color: #2196f3;
}

.mechanism-card.preview-features {
  border-left-color: #ff9800;
}

.mechanism-card.incubating-features {
  border-left-color: #9c27b0;
}

.mechanism-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mechanism-icon {
  font-size: 1.5rem;
}

.mechanism-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.cost-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.cost-badge.low {
  background: #e8f5e8;
  color: #4caf50;
}

.cost-badge.medium {
  background: #e3f2fd;
  color: #2196f3;
}

.cost-badge.high {
  background: #fff3e0;
  color: #ff9800;
}

.mechanism-content p {
  margin: 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

/* 成本复杂度图表 */
.cost-complexity-chart {
  margin-top: 3rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.chart-container {
  margin-top: 1.5rem;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 200px;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  transition: height 0.8s ease;
  margin-bottom: 0.5rem;
}

.bar-label {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  writing-mode: horizontal-tb;
}

.chart-axis {
  text-align: center;
  margin-top: 1rem;
  color: #666;
  font-weight: 500;
}

/* Java 11 特性样式 */
.java11-features {
  margin-top: 2rem;
}

.feature-detail {
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.feature-icon {
  font-size: 2rem;
}

.feature-header h4 {
  margin: 0;
  flex: 1;
  font-size: 1.3rem;
}

.jep-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.feature-content {
  padding: 2rem;
}

.feature-description {
  margin-bottom: 2rem;
}

.feature-description p {
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
}

/* HTTP比较样式 */
.http-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.comparison-item {
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.comparison-item h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.http1-demo .request-sequence {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.request-item,
.response-item {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.request-item {
  background: #ffebee;
  color: #c62828;
}

.response-item {
  background: #e8f5e8;
  color: #2e7d32;
}

.arrow {
  color: #666;
  font-weight: bold;
}

.http2-demo .multiplexed-streams {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stream {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stream-label {
  font-weight: 600;
  color: #333;
  min-width: 70px;
}

.frame {
  padding: 0.25rem 0.5rem;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.demo-note {
  margin-top: 1rem;
  font-style: italic;
  color: #666;
  font-size: 0.9rem;
}

/* 单文件程序演示 */
.single-file-demo {
  margin: 2rem 0;
}

.demo-steps {
  display: flex;
  gap: 2rem;
}

.step {
  flex: 1;
  display: flex;
  gap: 1rem;
  min-width: 0; /* 允许flex子项收缩 */
}

.step-number {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  min-width: 0; /* 允许内容收缩 */
}

.step-content h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.code-snippet,
.command {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #333;
  overflow-x: auto;
  word-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
}

.command {
  background: #2d3748;
  color: #e2e8f0;
  border-color: #4a5568;
}

/* 移除模块样式 */
.removal-impact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.removed-modules,
.migration-guide {
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.removed-modules {
  background: #fff5f5;
  border-color: #fed7d7;
}

.migration-guide {
  background: #f0fff4;
  border-color: #c6f6d5;
}

.removed-modules h5,
.migration-guide h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.removed-modules ul {
  margin: 0;
  padding-left: 1.5rem;
}

.removed-modules li {
  margin-bottom: 0.5rem;
  color: #666;
}

.migration-example {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #333;
  overflow-x: auto;
  margin-top: 1rem;
}
</style>
